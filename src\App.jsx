import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar.jsx';
import HomePage from './pages/HomePage.jsx';

function App() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/services" element={<div className="p-8"><h1 className="text-3xl">Services - Page en construction</h1></div>} />
          <Route path="/about" element={<div className="p-8"><h1 className="text-3xl">À propos - Page en construction</h1></div>} />
          <Route path="/contact" element={<div className="p-8"><h1 className="text-3xl">Contact - Page en construction</h1></div>} />
        </Routes>
      </main>
    </div>
  );
}

export default App;