import React, { useState } from 'react';
import { Link } from 'react-router-dom';

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <img className="h-12 w-auto" src="/logo.svg" alt="Clinique Dentaire" />
              <span className="ml-3 text-xl font-bold text-blue-600">DentaLab</span>
            </Link>
          </div>
          
          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium">Accueil</Link>
            <Link to="/services" className="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium">Services</Link>
            <Link to="/about" className="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium">À propos</Link>
            <Link to="/contact" className="text-gray-700 hover:text-blue-600 px-3 py-2 font-medium">Contact</Link>
            <a href="tel:+33123456789" className="bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700">
              Prendre RDV
            </a>
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 focus:outline-none"
            >
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link to="/" className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium">Accueil</Link>
            <Link to="/services" className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium">Services</Link>
            <Link to="/about" className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium">À propos</Link>
            <Link to="/contact" className="block px-3 py-2 text-gray-700 hover:text-blue-600 font-medium">Contact</Link>
            <a href="tel:+33123456789" className="block px-3 py-2 text-blue-600 font-medium">Prendre RDV</a>
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navbar;